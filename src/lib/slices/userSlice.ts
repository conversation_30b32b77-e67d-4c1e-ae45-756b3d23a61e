import {createAsyncThunk, createSlice} from '@reduxjs/toolkit';

import {apiRequest} from '@/utils/AxiosUtils';
import {RootState} from '@/lib/store';
import {Completable, pending} from '@/interfaces';
import {UsersWebDTO} from '@/models';

const INITIAL_STATE: Completable<UsersWebDTO> = pending();

export const fetchProfile = createAsyncThunk('user/fetch', async (user, {rejectWithValue}) => {
    const response = await apiRequest('/api/secured/user/profile');

    if (response.error) {
        rejectWithValue(response);
    }

    // console.log(response);
    // return {
    //     ...response,
    //     value: {
    //         ...response.value,
    //         roles: [
    //             // 'KASH_ACCOUNTS',
    //             // 'KASH_ADMIN',
    //             // 'KASH_COLLECTIONS:MANAGER',
    //             // 'KASH_SUPPORT:MANAGER',
    //             'KASH_POWERUSER',
    //             // 'KASH_UNDERWRITING',
    //             'KASH_OPERATOR',
    //         ],
    //     },
    // };
    return response;
});

export const userSlice = createSlice({
    name: 'user',
    initialState: INITIAL_STATE,
    // The `reducers` field lets us define reducers and generate associated actions
    reducers: {
        resetUserState: (state) => {
            state.code = 401;
            state.value = undefined;
            return state;
        },
    },
    extraReducers: (builder) => {
        builder
            .addCase(fetchProfile.pending, () => {
                return pending();
            })
            .addCase(fetchProfile.fulfilled, (state, action) => {
                return action.payload;
            });
    },
});

export const selectUser = (state: RootState) => state.user;

export const {resetUserState} = userSlice.actions;
export default userSlice.reducer;
