export const toCurrency = (amount: number | string | null | undefined) => {
    const numericAmount = Number(amount);

    if (isNaN(numericAmount) || (amount !== 0 && !amount)) {
        return '';
    }

    return '$' + numericAmount.toFixed(2).replace(/\d(?=(\d{3})+\.)/g, '$&,');
};

export const toCurrencyNoDecimals = (amount: number) => {
    return '$' + amount.toString().replace(/\B(?=(\d{3})+(?!\d))/g, ',');
};

export const toPercentage = (percent: number, precision = 2) => {
    return Number(percent * 100).toFixed(precision) + '%';
};

export const roundRate = (value: number | undefined) => {
    if (!value || isNaN(value)) {
        return '';
    }

    return (Math.ceil(value * 100_000) / 100_000).toFixed(5);
};

export const toCurrencyCompact = (value: number | null | undefined, precision = 1): string => {
    const numericValue = Number(value);

    if (isNaN(numericValue) || (value !== 0 && !value)) {
        return '';
    }

    const absValue = Math.abs(numericValue);
    const formatValue = (val: number) => '$' + parseFloat(val.toFixed(precision)).toString();

    if (absValue >= 1_000_000_000) {
        return formatValue(numericValue / 1_000_000_000) + 'B';
    } else if (absValue >= 1_000_000) {
        return formatValue(numericValue / 1_000_000) + 'M';
    } else if (absValue >= 1_000) {
        return formatValue(numericValue / 1_000) + 'K';
    }

    return formatValue(numericValue);
};
