export interface OriginationLogModel {
    gid: number;
    job_type: string;
    job_status: string;
    job_detail: string;
    job_context: string | null;
    job_start_time: string;
    job_end_time: string;
    job_document_id: number | null;
    job_user_id: number | null;
    job_user_name: string | null;
    employer_gid: number | null;
    employer_name: string | null;
    rollback: boolean;
    source: string | null;
}

export interface OriginationLogRecordModel {
    key: string;
    completed: boolean | null;
    message: string;
}

export interface OriginationLogSummaryModel {
    amount: string;
    status: string;
    count: number;
    batch_job_id: number;
}

export interface OriginationReviewModel {
    gid: number;
    loan_id: number;
    employee_id: number;
    application_id: number;
    origination_id: string;
    source: string;
    status: string;
    first_name: string;
    last_name: string;
    detailed: boolean;
    create_time: string;
    last_update_time: string;
    loan_date: string;
    funding_date: string;
    purchase_date: null | string;
    retained: null | boolean;
    fundable: null | boolean;
    disbursement_amount: null | string;
    loan_amount: null | string;
    external_status: string;
    status_detailed: boolean;
    state: null | string;
    rails_detailed: boolean;
    rules_detailed: boolean;
}

export interface OriginationReviewRuleModel {
    rule: string;
    description: string;
    date: string;
    result: boolean;
}

export interface OriginationReviewUpdateModel {
    loan_id: number;
    status: string;
    date: string;
}

export interface OriginationReviewAttachmentModel {
    filename: string;
    date: string;
}

export interface OriginationReviewRequiredDocsModel {
    name: string;
    received: boolean;
}

export interface OriginationReviewLoanModel {
    id: string;
    rail_type: string;
    priority: number;
    amount: number;
    code: string;
    transaction_type: string;
    account_type: string;
    description: string;
    account_name: string;
    processed: string[];
    returned: string[];
}

export interface OriginationReviewDetailsModel {
    failed_rules: OriginationReviewRuleModel[];
    status_updates: OriginationReviewUpdateModel[];
    funding_attempts: string[];
    attachments: OriginationReviewAttachmentModel[];
    required_docs: OriginationReviewRequiredDocsModel[];
    rails: {
        Loan: OriginationReviewLoanModel;
    };
}

export interface OriginationFundingSnapshotModel {
    date: string;
    amount: number;
    origination_source: string;
}

export interface OriginationFundingBalanceModel {
    balance: number;
    last_update_time: string;
    origination_source: string;
}

export interface OriginationFundingChartDataModel {
    date: string;
    crb_amount: number | null;
    brb_amount: number | null;
    crb_balance: number;
    brb_balance: number;
}
