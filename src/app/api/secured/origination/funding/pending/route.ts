import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {AxiosError} from 'axios';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationFundingPendingModel} from '@/interfaces';

const getFundingPending = (params: string) => {
    const url = `/secured/origination/funding${params ? `?${params}` : ''}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationFundingPendingModel[]>>(url)
        .then((res) => mapSucceeded(res, ({data = []}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest) {
    const searchParams = request.nextUrl.searchParams;
    const params = searchParams.toString();
    const response = await getFundingPending(params);

    return NextResponse.json(response, {status: response.code});
}
