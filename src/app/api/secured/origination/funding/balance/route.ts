import {NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {AxiosError} from 'axios';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationFundingBalanceModel} from '@/interfaces';

const getFundingBalance = () => {
    const url = '/secured/origination/funding/balance';

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationFundingBalanceModel[]>>(url)
        .then((res) => mapSucceeded(res, ({data = []}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET() {
    const response = await getFundingBalance();

    return NextResponse.json(response, {status: response.code});
}
