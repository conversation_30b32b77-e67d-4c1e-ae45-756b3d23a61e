import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {AxiosError} from 'axios';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationFundingSnapshotModel} from '@/interfaces';

const getFundingSnapshot = (startDate: string, endDate: string) => {
    const url = `/secured/origination/funding/snapshot/${startDate}/${endDate}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationFundingSnapshotModel[]>>(url)
        .then((res) => mapSucceeded(res, ({data = []}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest) {
    const dateFrom = request.nextUrl.searchParams.get('dateFrom') || '';
    const dateTo = request.nextUrl.searchParams.get('dateTo') || '';
    const response = await getFundingSnapshot(dateFrom, dateTo);

    return NextResponse.json(response, {status: response.code});
}
