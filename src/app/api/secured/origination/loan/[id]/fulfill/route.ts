import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {AxiosError} from 'axios';

const fulfillLoan = (payload: string, id: string) => {
    const url = `/secured/origination/loan/${id}/fulfill`;

    return axiosInterceptorInstance
        .put<KashableResponseDTO<boolean>>(url, payload)
        .then((res) => mapSucceeded(res, ({data}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function PUT(request: NextRequest, {params}: {params: Promise<{id: string}>}) {
    const payload = await request.text();
    const {id} = await params;
    const response = await fulfillLoan(payload, id);

    return NextResponse.json(response, {status: response.code});
}
