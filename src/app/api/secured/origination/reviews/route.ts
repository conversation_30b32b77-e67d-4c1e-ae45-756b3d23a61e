import {NextRequest, NextResponse} from 'next/server';
import {errored, getParamValueAsBinary, mapSucceeded} from '@/utils/AxiosUtils';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationReviewModel} from '@/interfaces';
import {AxiosError} from 'axios';

const getReviews = (searchParams: string) => {
    const url = `/secured/origination/funding/review?${searchParams}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationReviewModel[]>>(url)
        .then((res) => mapSucceeded(res, ({data = []}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest) {
    const recent = getParamValueAsBinary(request.nextUrl.searchParams.get('recent'));
    const notExported = getParamValueAsBinary(request.nextUrl.searchParams.get('notExported'));
    const rejected = getParamValueAsBinary(request.nextUrl.searchParams.get('rejected'));
    const searchParams = new URLSearchParams({
        recent,
        notExported,
        rejected,
    }).toString();
    const response = await getReviews(searchParams);

    return NextResponse.json(response, {status: response.code});
}
