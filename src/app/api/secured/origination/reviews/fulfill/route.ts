import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {AxiosError} from 'axios';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';

const exportLoans = (payload: string) => {
    const url = '/secured/origination/fulfill';

    return axiosInterceptorInstance
        .put<KashableResponseDTO<boolean>>(url, payload, {
            headers: {
                'Content-Type': 'application/json',
            },
        })
        .then((res) => mapSucceeded(res, ({data}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function PUT(request: NextRequest) {
    const payload = await request.json();
    const response = await exportLoans(payload);

    return NextResponse.json(response, {status: response.code});
}
