import {NextRequest, NextResponse} from 'next/server';
import {errored, mapSucceeded} from '@/utils/AxiosUtils';
import {axiosInterceptorInstance} from '@/app/api/axiosInstance';
import {KashableResponseDTO} from '@/models';
import {OriginationReviewDetailsModel} from '@/interfaces';
import {AxiosError} from 'axios';

const getReviewDetails = (id: string) => {
    const url = `/secured/origination/detail/${id}`;

    return axiosInterceptorInstance
        .get<KashableResponseDTO<OriginationReviewDetailsModel>>(url)
        .then((res) => mapSucceeded(res, ({data}) => data))
        .catch((error: AxiosError<string>) => errored(error));
};

export async function GET(request: NextRequest, {params}: {params: Promise<{id: string}>}) {
    const {id} = await params;
    const response = await getReviewDetails(id);

    return NextResponse.json(response, {status: response.code});
}
