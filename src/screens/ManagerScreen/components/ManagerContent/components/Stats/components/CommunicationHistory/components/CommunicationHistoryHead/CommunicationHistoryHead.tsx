import React, {useEffect} from 'react';
import {Button, Grid2, Typography} from '@mui/material';
import {CommunicationHistoryValues, validationSchema} from './schema';
import {useFormik} from 'formik';
import {KasDatePickerFormField} from '@/components';
import dayjs from 'dayjs';
import {useStats} from './../../../../useStats';
import {useManager} from '@/screens/ManagerScreen/useManager';
import {ManagerItem} from '@/screens/ManagerScreen/interfaces';

export const CommunicationHistoryHead = () => {
    const {activeMenu} = useManager();
    const {loadCommunicationHistory, communicationHistoryState} = useStats();

    const onSubmit = async (values: CommunicationHistoryValues) => {
        await loadCommunicationHistory(values.date);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            date: dayjs().format(),
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (activeMenu === ManagerItem.STATS && !communicationHistoryState.data) {
            formik.handleSubmit();
        }
    }, [activeMenu, communicationHistoryState.data]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} mb={2} alignItems='center'>
                <Grid2 size={3}>
                    <Typography variant='h6'>Communication History</Typography>
                </Grid2>
                <Grid2 size={3}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='date'
                        label='Date'
                        disabled={communicationHistoryState.loading}
                    />
                </Grid2>
                <Grid2 size={3} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        loading={communicationHistoryState.loading}
                        disabled={!formik.isValid || communicationHistoryState.loading}>
                        Search
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
