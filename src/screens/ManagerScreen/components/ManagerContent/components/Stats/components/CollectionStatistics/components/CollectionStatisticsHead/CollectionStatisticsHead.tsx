import React from 'react';
import {Stack, Typography} from '@mui/material';
import {useStats} from './../../../../useStats';

export const CollectionStatisticsHead = () => {
    const {dateParams} = useStats();

    return (
        <Stack direction='row' alignItems='center' spacing={2} sx={{minHeight: '40px'}}>
            <Typography variant='h6'>Collection Statistics</Typography>
            <Typography variant='body1'>
                {dateParams.start?.format('MM/DD/YYYY')} - {dateParams.end?.format('MM/DD/YYYY')}
            </Typography>
        </Stack>
    );
};
