import './styles.scss';

import React from 'react';
import {useOrigination} from './../../useOrigination';
import {OriginationItemType} from './../../interfaces';
import {
    Logs,
    FundingAccountBalances,
    FundingAccountBalancesProvider,
    Reviews,
    ReviewsProvider,
} from './components';

export const OriginationContent = () => {
    const {activeMenu} = useOrigination();

    return (
        <div className='kas-origination-content'>
            <div hidden={activeMenu !== OriginationItemType.Logs}>
                <Logs />
            </div>
            <div hidden={activeMenu !== OriginationItemType.Review}>
                <ReviewsProvider>
                    <Reviews />
                </ReviewsProvider>
            </div>
            <div hidden={activeMenu !== OriginationItemType.Funding_Account_Balances}>
                <FundingAccountBalancesProvider>
                    <FundingAccountBalances />
                </FundingAccountBalancesProvider>
            </div>
        </div>
    );
};
