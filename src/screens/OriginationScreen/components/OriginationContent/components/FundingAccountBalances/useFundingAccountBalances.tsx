import React, {createContext, useCallback, useContext, useMemo, useState} from 'react';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {
    Completable,
    DataStateInterface,
    OriginationFundingBalanceModel,
    OriginationFundingChartDataModel,
    OriginationFundingSnapshotModel,
} from '@/interfaces';
import {getDateRange} from '@/utils/DateUtils';
import {DEFAULT_DATE_FORMAT} from '@/constants';

interface FundingAccountBalancesContextModel {
    snapshotState: DataStateInterface<OriginationFundingSnapshotModel[]>;
    balanceState: DataStateInterface<OriginationFundingBalanceModel[]>;
    loadFundingData: (date?: string) => Promise<void>;
    chartData: OriginationFundingChartDataModel[];
    hiddenBars: string[];
    updateHiddenBars: (bar: string) => void;
}

const FundingAccountBalancesContext = createContext<FundingAccountBalancesContextModel | undefined>(
    undefined,
);

export const FundingAccountBalancesProvider = ({children}: {children: React.ReactNode}) => {
    const [snapshotState, setSnapshotState] = useState(getDefaultState<OriginationFundingSnapshotModel[]>);
    const [balanceState, setBalanceState] = useState(getDefaultState<OriginationFundingBalanceModel[]>);
    const [hiddenBars, setHiddenBars] = useState<string[]>([]);
    const [dateParams, setDateParams] = useState(getDateRange());

    const updateHiddenBars = useCallback(
        (dataKey: string) => {
            const newValue = hiddenBars.includes(dataKey)
                ? hiddenBars.filter((key) => key !== dataKey)
                : [...hiddenBars, dataKey];

            setHiddenBars(newValue);
        },
        [hiddenBars],
    );

    const chartData = useMemo(() => {
        if (!snapshotState.data || !balanceState.data) {
            return [];
        }

        const kasRecords = snapshotState.data.filter((sh) => (sh.origination_source || 'KAS') === 'KAS');
        const crbRecords = snapshotState.data.filter((sh) => sh.origination_source === 'CRB');
        const brbRecords = snapshotState.data.filter((sh) => sh.origination_source === 'BRB');

        const brbBalance = balanceState.data.filter((sh) => sh.origination_source === 'BRB')[0];
        const crbBalance = balanceState.data.filter((sh) => sh.origination_source === 'CRB')[0];

        const fundingByDate: {[key: string]: OriginationFundingChartDataModel} = {};

        crbRecords.forEach((crb) => {
            fundingByDate[crb.date] = {
                date: crb.date,
                crb_amount: crb.amount,
                brb_amount: null,
                crb_balance: crbBalance?.balance || 0,
                brb_balance: brbBalance?.balance || 0,
            };
        });

        brbRecords.forEach((brb) => {
            if (brb.date in fundingByDate) {
                fundingByDate[brb.date]['brb_amount'] = brb.amount;
                fundingByDate[brb.date]['brb_balance'] = brbBalance?.balance || 0;
                fundingByDate[brb.date]['crb_balance'] = crbBalance?.balance || 0;
            } else {
                fundingByDate[brb.date] = {
                    date: brb.date,
                    crb_amount: null,
                    brb_amount: brb.amount,
                    crb_balance: crbBalance?.balance || 0,
                    brb_balance: brbBalance?.balance || 0,
                };
            }
        });

        kasRecords.forEach((em) => {
            if (!(em.date in fundingByDate)) {
                fundingByDate[em.date] = {
                    date: em.date,
                    crb_amount: null,
                    brb_amount: null,
                    crb_balance: crbBalance?.balance || 0,
                    brb_balance: brbBalance?.balance || 0,
                };
            }
        });

        return Object.values(fundingByDate).sort((a, b) => a.date.localeCompare(b.date));
    }, [snapshotState.data, balanceState.data]);

    const loadSnapshotData = async (params: string) => {
        const url = `/api/secured/origination/funding/snapshot?${params}`;

        setSnapshotState(getLoadingState(snapshotState));
        const response: Completable<OriginationFundingSnapshotModel[]> = await apiRequest(url);
        setSnapshotState(getLoadedState(response));
    };

    const loadBalanceData = async () => {
        const url = '/api/secured/origination/funding/balance';

        setBalanceState(getLoadingState(balanceState));
        const response: Completable<OriginationFundingBalanceModel[]> = await apiRequest(url);
        setBalanceState(getLoadedState(response));
    };

    const loadFundingData = async (date?: string) => {
        const curParams = date ? getDateRange(date) : dateParams;
        const params = new URLSearchParams({
            dateFrom: curParams.start!.format(DEFAULT_DATE_FORMAT),
            dateTo: curParams.end!.format(DEFAULT_DATE_FORMAT),
        }).toString();

        setDateParams(curParams);
        await Promise.all([loadSnapshotData(params), loadBalanceData()]);
    };

    const value: FundingAccountBalancesContextModel = {
        snapshotState,
        balanceState,
        loadFundingData,
        chartData,
        hiddenBars,
        updateHiddenBars,
    };

    return (
        <FundingAccountBalancesContext.Provider value={value}>
            {children}
        </FundingAccountBalancesContext.Provider>
    );
};

export function useFundingAccountBalances() {
    const context = useContext(FundingAccountBalancesContext);
    if (!context) {
        throw new Error('useFundingAccountBalances must be used within FundingAccountBalancesProvider');
    }
    return context;
}
