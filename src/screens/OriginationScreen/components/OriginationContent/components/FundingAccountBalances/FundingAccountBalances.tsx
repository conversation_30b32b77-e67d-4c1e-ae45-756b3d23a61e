import React from 'react';
import {Divider, Paper, Stack} from '@mui/material';
import {FundingAccountBalancesChartBar, FundingAccountBalancesHead} from './components';
import {useFundingAccountBalances} from './useFundingAccountBalances';
import {KasL<PERSON>dingBackDrop, KasLoadingError, KasNoResults, KasSwitch, KasSwitchWhen} from '@/components';
import {NoResultsView} from '@/views';
import Box from '@mui/material/Box';

export const FundingAccountBalances = () => {
    const {snapshotState, balanceState, chartData, loadFundingData} = useFundingAccountBalances();
    const loading = snapshotState.loading || balanceState.loading;
    const error = snapshotState.error || balanceState.error;
    const notLoaded = !snapshotState.data || !balanceState.data;

    return (
        <Paper elevation={0}>
            <Stack p={2} spacing={2}>
                <FundingAccountBalancesHead />
                <Divider />
                <Box position='relative'>
                    {loading && <KasLoadingBackDrop />}
                    <KasSwitch>
                        <KasSwitchWhen condition={!!error}>
                            <KasLoadingError view='contained' error={error} onTryAgain={loadFundingData} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={notLoaded}>
                            <NoResultsView text='Search result will appear here' />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={chartData && chartData.length > 0}>
                            <FundingAccountBalancesChartBar data={chartData} />
                        </KasSwitchWhen>
                        <KasSwitchWhen condition={chartData && chartData.length === 0}>
                            <KasNoResults text='No records found' p={2} bgcolor='var(--color-grey)' />
                        </KasSwitchWhen>
                    </KasSwitch>
                </Box>
            </Stack>
        </Paper>
    );
};
