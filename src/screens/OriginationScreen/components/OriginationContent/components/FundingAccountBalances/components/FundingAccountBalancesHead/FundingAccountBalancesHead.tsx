import React, {useEffect} from 'react';
import {Button, Grid2, Typography} from '@mui/material';
import {useFormik} from 'formik';
import {KasDatePickerFormField} from '@/components';
import dayjs from 'dayjs';
import {FundingHeadValues, validationSchema} from './schema';
import {useFundingAccountBalances} from './../../useFundingAccountBalances';
import {useOrigination} from '@/screens/OriginationScreen/useOrigination';
import {OriginationItemType} from '@/screens/OriginationScreen/interfaces';

export const FundingAccountBalancesHead = () => {
    const {activeMenu} = useOrigination();
    const {snapshotState, balanceState, loadFundingData} = useFundingAccountBalances();
    const loading = snapshotState.loading || balanceState.loading;

    const onSubmit = async (values: FundingHeadValues) => {
        await loadFundingData(values.date);
    };

    const formik = useFormik({
        validateOnMount: true,
        initialValues: {
            date: dayjs().format(),
        },
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (
            activeMenu === OriginationItemType.Funding_Account_Balances &&
            !snapshotState.data &&
            !balanceState.data &&
            !loading
        ) {
            formik.handleSubmit();
        }
    }, [activeMenu, snapshotState.data, balanceState.data, loading]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={3}>
                    <Typography variant='h6'>Funding Account Balances</Typography>
                </Grid2>
                <Grid2 size={3}>
                    <KasDatePickerFormField
                        formik={formik}
                        name='date'
                        label='Date'
                        disabled={loading}
                        disableFuture
                    />
                </Grid2>
                <Grid2 size={2} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        loading={loading}
                        disabled={!formik.isValid || loading}>
                        Apply
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
