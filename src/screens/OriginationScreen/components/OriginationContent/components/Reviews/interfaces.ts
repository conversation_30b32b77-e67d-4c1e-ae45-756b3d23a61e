import {OriginationReviewModel} from '@/interfaces';

export interface ReviewParamsModel {
    recent: boolean;
    notExported: boolean;
    rejected: boolean;
}

export enum ReviewCardType {
    Status_Updates = 'Status Updates',
    Attachments = 'Attachments',
    Required_Documents = 'Required Documents',
    Failed_Rules = 'Failed Compliance Rules',
}

export enum ReviewModalType {
    Export_Loans = 'Export Selected Loans',
    Refresh_Loans = 'Refresh Selected Loans',
    Review_Details = 'Review Details',
}

export interface UpdateSelectedLoansProps {
    items: number[];
    onSuccess: () => void;
}

export interface ReviewDetailsProps {
    data: OriginationReviewModel;
    visibleCards: ReviewCardType[];
}

export type ReviewModalProps =
    | {type: ReviewModalType.Export_Loans; props: UpdateSelectedLoansProps}
    | {type: ReviewModalType.Refresh_Loans; props: UpdateSelectedLoansProps}
    | {type: ReviewModalType.Review_Details; props: ReviewDetailsProps};
