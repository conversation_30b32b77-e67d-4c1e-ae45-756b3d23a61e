import {CellContext, ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn, getSelectColumn} from '@/utils/TableUtils';
import {OriginationReviewModel} from '@/interfaces';
import {KasUnderwritingSharedLink} from '@/components';
import React from 'react';
import {ReviewActionCell} from './../components';

const columnHelper = createColumnHelper<OriginationReviewModel>();

const _defaultInfoColumn = defaultInfoColumn<OriginationReviewModel>;

export const ReviewsColumns = [
    getSelectColumn<OriginationReviewModel>(),
    _defaultInfoColumn('loan_id', 'Loan ID'),
    columnHelper.accessor((data) => `${data.first_name} ${data.last_name} [${data.employee_id}]`, {
        id: 'employee_id',
        header: 'Borrower',
        cell: (props) => {
            const {first_name, last_name, employee_id} = props.row.original;

            return (
                <>
                    {first_name} {last_name} [<KasUnderwritingSharedLink id={employee_id} />]
                </>
            );
        },
        meta: {
            exportHTML: (cell) => {
                const {first_name, last_name, employee_id} = cell.row.original;

                return `${first_name} ${last_name} [${employee_id}]`;
            },
        },
    }),
    _defaultInfoColumn('loan_date', 'Loan Date'),
    _defaultInfoColumn('source', 'LOS'),
    _defaultInfoColumn('origination_id', 'LOS ID'),
    columnHelper.accessor((data) => `${data.status} ${data.external_status}`, {
        id: 'status',
        header: 'Status',
        cell: (props) => <abbr title={props.row.original.external_status}>{props.row.original.status}</abbr>,
        meta: {
            exportHTML: (cell) => cell.row.original.status,
        },
    }),
    _defaultInfoColumn('last_update_time', 'Last Updated'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<OriginationReviewModel, string>) => (
            <ReviewActionCell data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
] as ColumnDef<OriginationReviewModel, unknown>[];
