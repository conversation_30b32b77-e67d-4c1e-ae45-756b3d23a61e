import {ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {OriginationReviewRequiredDocsModel} from '@/interfaces';
import {KasFlaggedIcon} from '@/components';
import React from 'react';

const columnHelper = createColumnHelper<OriginationReviewRequiredDocsModel>();

const _defaultInfoColumn = defaultInfoColumn<OriginationReviewRequiredDocsModel>;

export const RequiredDocumentsColumns = [
    _defaultInfoColumn('name', 'Name'),
    columnHelper.accessor('received', {
        id: 'received',
        header: 'Received',
        cell: (props) => <KasFlaggedIcon flagged={props.getValue()} />,
    }),
] as ColumnDef<OriginationReviewRequiredDocsModel, unknown>[];
