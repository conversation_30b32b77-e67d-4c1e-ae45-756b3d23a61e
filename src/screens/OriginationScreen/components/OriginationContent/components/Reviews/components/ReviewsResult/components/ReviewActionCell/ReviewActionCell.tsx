import React from 'react';
import {Completable, OriginationReviewModel} from '@/interfaces';
import {Stack} from '@mui/material';
import {ApplicationTapeCell} from '@/views/application';
import {ActionCell} from '@/components/table/cells';
import {InsertDriveFile, SyncAlt, ZoomIn} from '@mui/icons-material';
import {GlobalModal, useGlobalModal} from '@/components';
import {apiRequest} from '@/utils/AxiosUtils';
import {DEFAULT_ERROR_MSG} from '@/constants';
import {useSnackbar} from '@/hooks/useSnackbar';
import {useReviews} from './../../../../useReviews';
import {ReviewCardType, ReviewModalType} from './../../../../interfaces';

export const ReviewActionCell = ({data}: {data: OriginationReviewModel}) => {
    const {showMessage} = useSnackbar();
    const {showGlobalModal, hideGlobalModal} = useGlobalModal();
    const {loadReviews, setOpenModal} = useReviews();
    const fulfillText = data.origination_id
        ? "Push updated loan information and documents to arix. This will trigger Arix's compliance engine and result in the loan becoming ready to fund, or failing Arix's rules."
        : "Create a loan in the Arix system and upload documents. This will trigger Arix's compliance engine and result in the loan becoming ready to fund, or failing Arix's rules.";
    const showFulfillButton = data.source !== 'KAS';
    const showStatusButton = data.detailed && data.status_detailed;
    const showRulesButton = data.detailed && data.rules_detailed;

    const onFulfillLoan = async (comment: string) => {
        const response: Completable<boolean> = await apiRequest(
            `/api/secured/origination/loan/${data.loan_id}/fulfill`,
            {
                method: 'put',
                body: comment,
            },
        );

        if (response.value) {
            showMessage('Operation successful', 'success');
            hideGlobalModal();
            loadReviews().then();
        } else {
            showMessage(response.error || DEFAULT_ERROR_MSG, 'error');
        }
    };

    return (
        <Stack direction='row' spacing={1}>
            <ApplicationTapeCell gid={data.application_id} />
            {showStatusButton && (
                <ActionCell
                    Icon={<ZoomIn titleAccess='See Status Details' />}
                    onClick={() =>
                        setOpenModal({
                            type: ReviewModalType.Review_Details,
                            props: {
                                data,
                                visibleCards: [
                                    ReviewCardType.Status_Updates,
                                    ReviewCardType.Attachments,
                                    ReviewCardType.Required_Documents,
                                ],
                            },
                        })
                    }
                />
            )}
            {showRulesButton && (
                <ActionCell
                    Icon={<InsertDriveFile titleAccess='See Failed Rules' />}
                    onClick={() =>
                        setOpenModal({
                            type: ReviewModalType.Review_Details,
                            props: {data, visibleCards: [ReviewCardType.Failed_Rules]},
                        })
                    }
                />
            )}
            {showFulfillButton && (
                <ActionCell
                    Icon={<SyncAlt color='primary' titleAccess={fulfillText} />}
                    onClick={() =>
                        showGlobalModal({
                            type: GlobalModal.Comment,
                            props: {
                                title: 'Update Arix',
                                onSubmit: (values) => onFulfillLoan(values.comment),
                            },
                        })
                    }
                />
            )}
        </Stack>
    );
};
