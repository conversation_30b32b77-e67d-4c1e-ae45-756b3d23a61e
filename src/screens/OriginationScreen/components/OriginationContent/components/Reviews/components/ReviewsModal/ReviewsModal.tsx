import React, {useMemo} from 'react';
import {KasModal} from '@/components';
import {ReviewDetails, UpdateLoans} from './components';
import {useReviews} from './../../useReviews';
import {ReviewModalType} from './../../interfaces';

export const ReviewsModal = () => {
    const {openModal, setOpenModal} = useReviews();

    const title = useMemo(() => {
        switch (openModal?.type) {
            case ReviewModalType.Review_Details:
                return `Origination Status for Loan Id: ${openModal.props.data.loan_id} (${openModal.props.data.external_status})`;
            default:
                return openModal?.type || 'Action Modal';
        }
    }, [openModal?.type]);

    const size = useMemo(() => {
        switch (openModal?.type) {
            case ReviewModalType.Review_Details:
                return 'large';
            case ReviewModalType.Export_Loans:
            case ReviewModalType.Refresh_Loans:
                return 'small';
            default:
                return 'medium';
        }
    }, [openModal?.type]);

    const renderForm = useMemo(() => {
        switch (openModal?.type) {
            case ReviewModalType.Export_Loans:
                return (
                    <UpdateLoans
                        title='Re-export all selected loans?'
                        url='/api/secured/origination/reviews/fulfill'
                        {...openModal.props}
                    />
                );
            case ReviewModalType.Refresh_Loans:
                return (
                    <UpdateLoans
                        title='Refresh all selected loans?'
                        url='/api/secured/origination/reviews/refresh'
                        {...openModal.props}
                    />
                );
            case ReviewModalType.Review_Details:
                return <ReviewDetails {...openModal.props} />;
            default:
                return null;
        }
    }, [openModal?.type]);

    return (
        <KasModal title={title} size={size} open={!!openModal} onClose={() => setOpenModal(null)}>
            {renderForm}
        </KasModal>
    );
};
