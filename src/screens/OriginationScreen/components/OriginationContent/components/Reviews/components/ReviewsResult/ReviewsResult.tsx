import React, {useMemo} from 'react';
import {ReviewsColumns} from './tables';
import {TableView} from '@/views';
import {OriginationReviewModel} from '@/interfaces';
import {useReviews} from '../../useReviews';
import {Button, Stack} from '@mui/material';
import {useOrigination} from '@/screens/OriginationScreen/useOrigination';
import {OriginationItemType} from '@/screens/OriginationScreen/interfaces';
import {ReviewModalType} from './../../interfaces';

export const ReviewsResult = () => {
    const {reviewsState, setSelectedReviews, selectedReviews, setOpenModal} = useReviews();
    const {changeActiveMenu, loadLogs} = useOrigination();

    const items = useMemo(() => {
        return selectedReviews.map((item) => item.gid);
    }, [selectedReviews]);

    const onSuccess = () => {
        changeActiveMenu(OriginationItemType.Logs);
        loadLogs().then();
        setSelectedReviews([]);
    };

    return (
        <TableView<OriginationReviewModel>
            withTableActions
            columns={ReviewsColumns}
            loading={reviewsState.loading}
            error={reviewsState.error}
            data={reviewsState.data}
            tableName='Reviews Table'
            sortingColumns={[{id: 'origination_id', desc: false}]}
            onRowSelectionChange={setSelectedReviews}
            tableActions={
                <Stack direction='row' spacing={1}>
                    <Button
                        variant='text'
                        disabled={!items.length}
                        onClick={() =>
                            setOpenModal({
                                type: ReviewModalType.Refresh_Loans,
                                props: {items, onSuccess},
                            })
                        }>
                        REFRESH SELECTED
                    </Button>
                    <Button
                        variant='text'
                        disabled={!items.length}
                        onClick={() =>
                            setOpenModal({
                                type: ReviewModalType.Export_Loans,
                                props: {items, onSuccess},
                            })
                        }>
                        EXPORT SELECTED
                    </Button>
                </Stack>
            }
        />
    );
};
