import React, {useEffect, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {OriginationReviewDetailsModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {KasLoading, KasLoadingError, KasSwitch, KasSwitchWhen} from '@/components';
import {ReviewDetailsCard} from './components';
import {Grid2} from '@mui/material';
import {ReviewDetailsProps} from './../../../../interfaces';
import {NoResultsView} from '@/views';

export const ReviewDetails = ({data, visibleCards}: ReviewDetailsProps) => {
    const [dataState, setDataState] = useState(getDefaultState<OriginationReviewDetailsModel>);

    const loadData = async () => {
        const url = `/api/secured/origination/reviews/${data.gid}`;

        setDataState(getLoadingState(dataState));
        const response = await apiRequest(url);
        setDataState(getLoadedState(response));
    };

    useEffect(() => {
        loadData().then();
    }, []);

    return (
        <KasSwitch>
            <KasSwitchWhen condition={dataState.loading}>
                <KasLoading />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!dataState.error}>
                <KasLoadingError error={dataState.error} />
            </KasSwitchWhen>
            <KasSwitchWhen condition={!!dataState.data}>
                {!!dataState.data ? (
                    <Grid2 container spacing={2}>
                        {visibleCards.map((cardType) => (
                            <Grid2 key={cardType} size={12}>
                                <ReviewDetailsCard cardType={cardType} detailsData={dataState.data!} />
                            </Grid2>
                        ))}
                    </Grid2>
                ) : (
                    <NoResultsView text='No cards to display.' />
                )}
            </KasSwitchWhen>
        </KasSwitch>
    );
};
