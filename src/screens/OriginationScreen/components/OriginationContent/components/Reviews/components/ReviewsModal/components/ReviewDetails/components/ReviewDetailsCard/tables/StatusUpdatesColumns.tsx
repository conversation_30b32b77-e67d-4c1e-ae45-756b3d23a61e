import {ColumnDef} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {OriginationReviewUpdateModel} from '@/interfaces';

const _defaultInfoColumn = defaultInfoColumn<OriginationReviewUpdateModel>;

export const StatusUpdatesColumns = [
    _defaultInfoColumn('date', 'Date'),
    _defaultInfoColumn('status', 'Status'),
] as ColumnDef<OriginationReviewUpdateModel, unknown>[];
