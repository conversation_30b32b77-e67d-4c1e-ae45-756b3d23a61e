import {ColumnDef} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {OriginationReviewAttachmentModel} from '@/interfaces';

const _defaultInfoColumn = defaultInfoColumn<OriginationReviewAttachmentModel>;

export const AttachmentsColumns = [
    _defaultInfoColumn('filename', 'Filename'),
    _defaultInfoColumn('date', 'Date'),
] as ColumnDef<OriginationReviewAttachmentModel, unknown>[];
