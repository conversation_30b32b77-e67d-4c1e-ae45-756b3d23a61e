import {ColumnDef} from '@tanstack/react-table';
import {defaultInfoColumn} from '@/utils/TableUtils';
import {OriginationReviewRuleModel} from '@/interfaces';

const _defaultInfoColumn = defaultInfoColumn<OriginationReviewRuleModel>;

export const FailedRulesColumns = [
    _defaultInfoColumn('date', 'Date'),
    _defaultInfoColumn('rule', 'Rule'),
    _defaultInfoColumn('description', 'Description'),
] as ColumnDef<OriginationReviewRuleModel, unknown>[];
