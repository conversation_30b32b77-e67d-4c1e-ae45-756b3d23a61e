import React, {useMemo} from 'react';
import {Divider, Grid2, Paper, Typography} from '@mui/material';
import {ReviewCardType} from './../../../../../../interfaces';
import {
    AttachmentsColumns,
    FailedRulesColumns,
    RequiredDocumentsColumns,
    StatusUpdatesColumns,
} from './tables';
import {KasDefaultTable} from '@/components';
import {
    OriginationReviewAttachmentModel,
    OriginationReviewDetailsModel,
    OriginationReviewRequiredDocsModel,
    OriginationReviewRuleModel,
    OriginationReviewUpdateModel,
} from '@/interfaces';

interface ReviewDetailsCardProps {
    cardType: ReviewCardType;
    detailsData: OriginationReviewDetailsModel;
}

export const ReviewDetailsCard = ({cardType, detailsData}: ReviewDetailsCardProps) => {
    const renderContent = useMemo(() => {
        switch (cardType) {
            case ReviewCardType.Status_Updates:
                return (
                    <KasDefaultTable<OriginationReviewUpdateModel>
                        columns={StatusUpdatesColumns}
                        data={detailsData.status_updates || []}
                        tableName='Status Updates'
                        sortingColumns={[{id: 'date', desc: true}]}
                    />
                );
            case ReviewCardType.Attachments:
                return (
                    <KasDefaultTable<OriginationReviewAttachmentModel>
                        columns={AttachmentsColumns}
                        data={detailsData.attachments || []}
                        tableName='Attachments'
                        sortingColumns={[{id: 'date', desc: true}]}
                    />
                );
            case ReviewCardType.Required_Documents:
                return (
                    <KasDefaultTable<OriginationReviewRequiredDocsModel>
                        columns={RequiredDocumentsColumns}
                        data={detailsData.required_docs || []}
                        tableName='Required Documents'
                        sortingColumns={[{id: 'received', desc: true}]}
                    />
                );
            case ReviewCardType.Failed_Rules:
                return (
                    <KasDefaultTable<OriginationReviewRuleModel>
                        columns={FailedRulesColumns}
                        data={detailsData.failed_rules || []}
                        tableName='Failed Rules'
                        sortingColumns={[{id: 'date', desc: true}]}
                    />
                );
            default:
                return null;
        }
    }, [cardType, detailsData]);

    return (
        <Paper elevation={0}>
            <Grid2 container p={1.5} rowSpacing={1} spacing={2}>
                <Grid2 size={12}>
                    <Typography variant='subtitle1'>{cardType}</Typography>
                </Grid2>
                <Grid2 size={12} mb={1}>
                    <Divider />
                </Grid2>
                <Grid2 size={12}>{renderContent}</Grid2>
            </Grid2>
        </Paper>
    );
};
