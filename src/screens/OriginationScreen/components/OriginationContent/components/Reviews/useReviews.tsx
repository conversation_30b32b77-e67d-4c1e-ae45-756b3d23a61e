import React, {createContext, useContext, useState} from 'react';
import {DataStateInterface, OriginationReviewModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {ReviewModalProps, ReviewParamsModel} from './interfaces';
import {DEFAULT_FILTER_PARAMS} from './data';

interface ReviewsContextModel {
    reviewsState: DataStateInterface<OriginationReviewModel[]>;
    loadReviews: (params?: ReviewParamsModel) => Promise<void>;
    selectedReviews: OriginationReviewModel[];
    setSelectedReviews: (value: OriginationReviewModel[]) => void;
    openModal: ReviewModalProps | null;
    setOpenModal: (value: ReviewModalProps | null) => void;
}

const ReviewsContext = createContext<ReviewsContextModel | undefined>(undefined);

export const ReviewsProvider = ({children}: {children: React.ReactNode}) => {
    const [reviewsState, setReviewsState] =
        useState<DataStateInterface<OriginationReviewModel[]>>(getDefaultState<OriginationReviewModel[]>());
    const [filterParams, setFilterParams] = useState<ReviewParamsModel>(DEFAULT_FILTER_PARAMS);
    const [selectedReviews, setSelectedReviews] = useState<OriginationReviewModel[]>([]);
    const [openModal, setOpenModal] = useState<ReviewModalProps | null>(null);

    const loadReviews = async (params?: ReviewParamsModel) => {
        const curParams = params || filterParams;
        const urlParams = new URLSearchParams({
            recent: String(curParams.recent),
            notExported: String(curParams.notExported),
            rejected: String(curParams.rejected),
        });
        const url = `/api/secured/origination/reviews?${urlParams.toString()}`;

        setFilterParams(curParams);
        setReviewsState(getLoadingState(reviewsState));
        const response = await apiRequest(url);
        setReviewsState(getLoadedState(response));
    };

    const value: ReviewsContextModel = {
        reviewsState,
        loadReviews,
        selectedReviews,
        setSelectedReviews,
        openModal,
        setOpenModal,
    };

    return <ReviewsContext.Provider value={value}>{children}</ReviewsContext.Provider>;
};

export function useReviews() {
    const context = useContext(ReviewsContext);
    if (!context) {
        throw new Error('useReviews must be used within ReviewsProvider');
    }
    return context;
}
