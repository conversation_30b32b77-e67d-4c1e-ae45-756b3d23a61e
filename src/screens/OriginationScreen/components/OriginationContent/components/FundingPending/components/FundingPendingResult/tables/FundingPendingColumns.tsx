import {CellContext, ColumnDef, createColumnHelper} from '@tanstack/react-table';
import {defaultInfoColumn, getSelectColumn} from '@/utils/TableUtils';
import {OriginationFundingPendingModel} from '@/interfaces';
import {KasUnderwritingSharedLink} from '@/components';
import React from 'react';
import {FundingPendingActionCell} from '../components';

const columnHelper = createColumnHelper<OriginationFundingPendingModel>();

const _defaultInfoColumn = defaultInfoColumn<OriginationFundingPendingModel>;

export const FundingPendingColumns = [
    getSelectColumn<OriginationFundingPendingModel>(),
    columnHelper.accessor('loan_id', {
        id: 'loan_id',
        header: 'Loan ID',
        cell: (props) => {
            const {loan_id, retained} = props.row.original;
            const retainedMark = retained ? '*' : '';
            return `${loan_id}${retainedMark}`;
        },
        meta: {
            exportHTML: (cell) => {
                const {loan_id, retained} = cell.row.original;
                const retainedMark = retained ? '*' : '';
                return `${loan_id}${retainedMark}`;
            },
        },
    }),
    columnHelper.accessor((data) => `${data.first_name} ${data.last_name} [${data.employee_id}]`, {
        id: 'employee_id',
        header: 'Borrower',
        cell: (props) => {
            const {first_name, last_name, employee_id} = props.row.original;

            return (
                <>
                    {first_name} {last_name} [<KasUnderwritingSharedLink id={employee_id} />]
                </>
            );
        },
        meta: {
            exportHTML: (cell) => {
                const {first_name, last_name, employee_id} = cell.row.original;
                return `${first_name} ${last_name} [${employee_id}]`;
            },
        },
    }),
    _defaultInfoColumn('loan_date', 'Loan Date'),
    _defaultInfoColumn('source', 'LOS'),
    columnHelper.accessor('origination_id', {
        id: 'origination_id',
        header: 'LOS ID',
        cell: (props) => props.row.original.origination_id || 'N/A',
        meta: {
            exportHTML: (cell) => cell.row.original.origination_id || 'N/A',
        },
    }),
    columnHelper.accessor((data) => `${data.status} ${data.external_status || ''}`, {
        id: 'status',
        header: 'Status',
        cell: (props) => (
            <abbr title={props.row.original.external_status || 'N/A'}>
                {props.row.original.status}
            </abbr>
        ),
        meta: {
            exportHTML: (cell) => cell.row.original.status,
        },
    }),
    _defaultInfoColumn('last_update_time', 'Last Updated'),
    {
        id: 'action',
        header: 'Action',
        cell: (props: CellContext<OriginationFundingPendingModel, string>) => (
            <FundingPendingActionCell data={props.row.original} />
        ),
        enableSorting: false,
        meta: {
            notExport: true,
        },
    },
    // Hidden columns for export
    _defaultInfoColumn('loan_amount', 'Loan Amount'),
    _defaultInfoColumn('disbursement_amount', 'Disbursement Amount'),
    _defaultInfoColumn('state', 'State'),
] as ColumnDef<OriginationFundingPendingModel, unknown>[];
