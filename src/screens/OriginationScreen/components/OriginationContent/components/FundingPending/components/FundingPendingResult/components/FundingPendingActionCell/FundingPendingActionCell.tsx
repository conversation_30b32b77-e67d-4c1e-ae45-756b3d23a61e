import React from 'react';
import {Button, Stack} from '@mui/material';
import {OriginationFundingPendingModel} from '@/interfaces';

interface FundingPendingActionCellProps {
    data: OriginationFundingPendingModel;
}

export const FundingPendingActionCell = ({data}: FundingPendingActionCellProps) => {
    const handleStatusDetails = () => {
        // TODO: Implement status details modal
        console.log('Status details for:', data.gid, data.loan_id, data.external_status);
    };

    const handleRailDetails = () => {
        // TODO: Implement rail details modal
        console.log('Rail details for:', data.gid, data.loan_id, data.external_status);
    };

    const handleRulesDetails = () => {
        // TODO: Implement rules details modal
        console.log('Rules details for:', data.gid, data.loan_id, data.external_status);
    };

    return (
        <Stack direction='row' spacing={1}>
            <Button
                size='small'
                variant='outlined'
                onClick={handleStatusDetails}
                className='action-status-details'>
                Status
            </Button>
            <Button
                size='small'
                variant='outlined'
                onClick={handleRailDetails}
                className='action-rail-details'>
                Rail
            </Button>
            <Button
                size='small'
                variant='outlined'
                onClick={handleRulesDetails}
                className='action-rules-details'>
                Rules
            </Button>
        </Stack>
    );
};
