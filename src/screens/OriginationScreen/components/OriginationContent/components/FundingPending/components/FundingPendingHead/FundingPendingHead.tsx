import React, {useEffect} from 'react';
import {<PERSON>ton, <PERSON>pography, Grid2, FormControlLabel, Checkbox, Stack} from '@mui/material';
import {useFormik} from 'formik';
import {validationSchema} from './schema';
import {useFundingPending} from '../../useFundingPending';
import {DEFAULT_FILTER_PARAMS} from './data';
import {FundingPendingParamsModel} from '@/screens/OriginationScreen/interfaces';
import {useOrigination} from '@/screens/OriginationScreen/useOrigination';
import {OriginationItemType} from '@/screens/OriginationScreen/interfaces';

export const FundingPendingHead = () => {
    const {activeMenu} = useOrigination();
    const {loadFundingPending, fundingPendingState} = useFundingPending();

    const onSubmit = async (values: FundingPendingParamsModel) => {
        await loadFundingPending(values);
    };

    const formik = useFormik<FundingPendingParamsModel>({
        validateOnMount: true,
        initialValues: DEFAULT_FILTER_PARAMS,
        onSubmit,
        validationSchema,
    });

    useEffect(() => {
        if (activeMenu === OriginationItemType.Pending && !fundingPendingState.data && !fundingPendingState.loading) {
            formik.handleSubmit();
        }
    }, [activeMenu, fundingPendingState.data, fundingPendingState.loading]);

    return (
        <form onSubmit={formik.handleSubmit}>
            <Grid2 container spacing={2} alignItems='center'>
                <Grid2 size={3}>
                    <Typography variant='h6'>Funding Pending</Typography>
                </Grid2>
                <Grid2 size={7}>
                    <Stack flexDirection='row' useFlexGap flexWrap='wrap'>
                        <FormControlLabel
                            disabled={fundingPendingState.loading}
                            label='Show Recent Entries'
                            control={
                                <Checkbox
                                    size='small'
                                    name='recent'
                                    sx={{height: 28}}
                                    onChange={formik.handleChange}
                                    checked={formik.values.recent}
                                />
                            }
                        />
                        <FormControlLabel
                            disabled={fundingPendingState.loading}
                            label='Include Not Exported'
                            control={
                                <Checkbox
                                    size='small'
                                    name='notExported'
                                    sx={{height: 28}}
                                    onChange={formik.handleChange}
                                    checked={formik.values.notExported}
                                />
                            }
                        />
                        <FormControlLabel
                            disabled={fundingPendingState.loading}
                            label='Include Rejected'
                            control={
                                <Checkbox
                                    size='small'
                                    name='rejected'
                                    sx={{height: 28}}
                                    onChange={formik.handleChange}
                                    checked={formik.values.rejected}
                                />
                            }
                        />
                    </Stack>
                </Grid2>
                <Grid2 size={2} ml='auto'>
                    <Button
                        fullWidth
                        variant='contained'
                        type='submit'
                        disabled={!formik.isValid || fundingPendingState.loading}>
                        Apply
                    </Button>
                </Grid2>
            </Grid2>
        </form>
    );
};
