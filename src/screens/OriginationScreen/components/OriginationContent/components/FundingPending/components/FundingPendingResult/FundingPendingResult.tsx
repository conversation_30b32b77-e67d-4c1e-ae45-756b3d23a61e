import React, {useMemo} from 'react';
import {<PERSON><PERSON>, Divider, Paper, Stack, Typography} from '@mui/material';
import {useFundingPending} from '../../useFundingPending';
import {
    KasLoadingBackDrop,
    KasLoadingError,
    KasNoResults,
    KasSwitch,
    KasSwitchWhen,
    KasDefaultTable,
} from '@/components';
import {NoResultsView} from '@/views';
import {FundingPendingColumns} from './tables/FundingPendingColumns';
import {OriginationFundingPendingModel} from '@/interfaces';
import {toCurrencyNoDecimals} from '@/utils/FormatUtils';

export const FundingPendingResult = () => {
    const {
        fundingPendingState,
        selectedLoanIds,
        selectedGids,
        totalDisbursementAmount,
        fundingDisabled,
        reviewDisabled,
        updateSelection,
        clearItems,
        fundSelected,
        refreshSelected,
        loadFundingPending,
    } = useFundingPending();

    const loading = fundingPendingState.loading;
    const error = fundingPendingState.error;
    const data = fundingPendingState.data;

    const handleRowSelectionChange = (selectedData: OriginationFundingPendingModel[]) => {
        updateSelection(selectedData);
    };

    const handleFundLoans = async () => {
        if (selectedLoanIds.length > 0) {
            try {
                await fundSelected(selectedLoanIds);
                // Refresh the data after funding
                await loadFundingPending();
                clearItems();
            } catch (error) {
                console.error('Failed to fund loans:', error);
            }
        }
    };

    const handleRefreshLoans = async () => {
        if (selectedGids.length > 0) {
            try {
                await refreshSelected(selectedGids);
                // Refresh the data after refresh action
                await loadFundingPending();
                clearItems();
            } catch (error) {
                console.error('Failed to refresh loans:', error);
            }
        }
    };

    const footerContent = useMemo(() => {
        if (!data) return null;

        const totalLoanAmount = data.reduce((sum, item) => sum + item.loan_amount, 0);
        const totalDisbursementAmount = data.reduce((sum, item) => sum + item.disbursement_amount, 0);

        return (
            <Stack direction='row' spacing={2} justifyContent='flex-end' p={2}>
                <Typography variant='body2'>
                    To be Funded: {toCurrencyNoDecimals(totalDisbursementAmount)}
                </Typography>
                <Typography variant='body2'>
                    Total: {toCurrencyNoDecimals(totalLoanAmount)}
                </Typography>
            </Stack>
        );
    }, [data]);

    const selectedActions = (
        <Stack direction='row' spacing={2} p={2}>
            <Button
                variant='contained'
                color='primary'
                disabled={fundingDisabled}
                onClick={handleFundLoans}>
                Fund Selected Loans ({selectedLoanIds.length})
            </Button>
            <Button
                variant='outlined'
                disabled={reviewDisabled}
                onClick={handleRefreshLoans}>
                Refresh Selected ({selectedGids.length})
            </Button>
            <Button
                variant='outlined'
                onClick={clearItems}
                disabled={selectedGids.length === 0}>
                Clear Selection
            </Button>
        </Stack>
    );

    return (
        <Paper>
            <KasSwitch>
                <KasSwitchWhen condition={loading}>
                    <KasLoadingBackDrop />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!error}>
                    <KasLoadingError onRetry={() => loadFundingPending()} />
                </KasSwitchWhen>
                <KasSwitchWhen condition={!data || data.length === 0}>
                    <NoResultsView>
                        <KasNoResults />
                    </NoResultsView>
                </KasSwitchWhen>
                <KasSwitchWhen condition={!!data && data.length > 0}>
                    <Stack>
                        {selectedGids.length > 0 && (
                            <>
                                {selectedActions}
                                <Divider />
                            </>
                        )}
                        <KasDefaultTable
                            data={data || []}
                            columns={FundingPendingColumns}
                            tableName='funding-pending'
                            onRowSelectionChange={handleRowSelectionChange}
                        />
                        {footerContent}
                    </Stack>
                </KasSwitchWhen>
            </KasSwitch>
        </Paper>
    );
};
