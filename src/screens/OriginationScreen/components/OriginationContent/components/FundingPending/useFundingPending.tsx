import React, {createContext, useContext, useState} from 'react';
import {apiRequest} from '@/utils/AxiosUtils';
import {Completable, DataStateInterface, OriginationFundingPendingModel} from '@/interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {FundingPendingParamsModel} from '@/screens/OriginationScreen/interfaces';

interface FundingPendingContextModel {
    fundingPendingState: DataStateInterface<OriginationFundingPendingModel[]>;
    loadFundingPending: (params?: FundingPendingParamsModel) => Promise<void>;
    selectedLoanIds: number[];
    selectedGids: number[];
    totalDisbursementAmount: number;
    fundingDisabled: boolean;
    reviewDisabled: boolean;
    updateSelection: (selectedItems: OriginationFundingPendingModel[]) => void;
    clearItems: () => void;
    fundSelected: (loanIds: number[]) => Promise<void>;
    refreshSelected: (gids: number[]) => Promise<void>;
}

const FundingPendingContext = createContext<FundingPendingContextModel | undefined>(undefined);

export const FundingPendingProvider = ({children}: {children: React.ReactNode}) => {
    const [fundingPendingState, setFundingPendingState] = useState<
        DataStateInterface<OriginationFundingPendingModel[]>
    >(getDefaultState<OriginationFundingPendingModel[]>());
    const [filterParams, setFilterParams] = useState<FundingPendingParamsModel>();
    
    // Selection state
    const [selectedLoanIds, setSelectedLoanIds] = useState<number[]>([]);
    const [selectedGids, setSelectedGids] = useState<number[]>([]);
    const [totalDisbursementAmount, setTotalDisbursementAmount] = useState<number>(0);
    const [fundingDisabled, setFundingDisabled] = useState<boolean>(true);
    const [reviewDisabled, setReviewDisabled] = useState<boolean>(true);

    const loadFundingPending = async (params?: FundingPendingParamsModel) => {
        const curParams = params || filterParams;
        const urlParams = new URLSearchParams();
        
        if (curParams?.recent) urlParams.append('recent', 'true');
        if (curParams?.notExported) urlParams.append('notExported', 'true');
        if (curParams?.rejected) urlParams.append('rejected', 'true');
        
        const url = `/api/secured/origination/funding/pending?${urlParams.toString()}`;

        setFilterParams(curParams);
        setFundingPendingState(getLoadingState(fundingPendingState));
        const response: Completable<OriginationFundingPendingModel[]> = await apiRequest(url);
        
        // Mark all items as unselected
        if (response.value) {
            response.value = response.value.map(item => ({...item, selected: false}));
        }
        
        setFundingPendingState(getLoadedState(response));
        clearItems();
    };

    const updateSelection = (selectedItems: OriginationFundingPendingModel[]) => {
        // Calculate new selection state
        const newSelectedGids = selectedItems.map(item => item.gid);
        const fundableItems = selectedItems.filter(item => item.fundable);
        const newSelectedLoanIds = fundableItems.map(item => item.loan_id);
        const newTotalDisbursementAmount = fundableItems.reduce((sum, item) => sum + item.disbursement_amount, 0);

        // Update selection arrays
        setSelectedGids(newSelectedGids);
        setSelectedLoanIds(newSelectedLoanIds);
        setTotalDisbursementAmount(newTotalDisbursementAmount);

        // Update button states
        const reviewableCount = newSelectedGids.length;
        const fundableCount = newSelectedLoanIds.length;

        setReviewDisabled(reviewableCount === 0);
        setFundingDisabled(fundableCount === 0 || fundableCount < reviewableCount);
    };

    const clearItems = () => {
        setSelectedLoanIds([]);
        setSelectedGids([]);
        setTotalDisbursementAmount(0);
        setFundingDisabled(true);
        setReviewDisabled(true);
    };

    const fundSelected = async (loanIds: number[]) => {
        // TODO: Implement funding API call
        const url = '/api/secured/origination/funding/fund';
        const response = await apiRequest(url, {
            method: 'POST',
            body: JSON.stringify({loan_ids: loanIds})
        });
        return response;
    };

    const refreshSelected = async (gids: number[]) => {
        // TODO: Implement refresh API call
        const url = '/api/secured/origination/funding/refresh';
        const response = await apiRequest(url, {
            method: 'POST',
            body: JSON.stringify({gids})
        });
        return response;
    };

    const value: FundingPendingContextModel = {
        fundingPendingState,
        loadFundingPending,
        selectedLoanIds,
        selectedGids,
        totalDisbursementAmount,
        fundingDisabled,
        reviewDisabled,
        updateSelection,
        clearItems,
        fundSelected,
        refreshSelected,
    };

    return <FundingPendingContext.Provider value={value}>{children}</FundingPendingContext.Provider>;
};

export function useFundingPending() {
    const context = useContext(FundingPendingContext);
    if (!context) {
        throw new Error('useFundingPending must be used within FundingPendingProvider');
    }
    return context;
}
