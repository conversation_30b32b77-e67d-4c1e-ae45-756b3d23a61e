import React, {createContext, useContext, useEffect, useState} from 'react';
import {useHashHandler} from '@/hooks/useHashHandler';
import {LogsFilterParamsModel, OriginationItemType} from './interfaces';
import {getDefaultState, getLoadedState, getLoadingState} from '@/utils/DataStateUtils';
import {apiRequest} from '@/utils/AxiosUtils';
import {DataStateInterface, OriginationLogModel} from '@/interfaces';

interface OriginationContextModel {
    activeMenu: OriginationItemType;
    changeActiveMenu: (value: OriginationItemType) => void;
    logsState: DataStateInterface<OriginationLogModel[]>;
    loadLogs: (params?: LogsFilterParamsModel) => Promise<void>;
}

const OriginationContext = createContext<OriginationContextModel | undefined>(undefined);

export const OriginationProvider = ({children}: {children: React.ReactNode}) => {
    const {hashMatch, updateRoute} = useHashHandler();
    const [activeMenu, setActiveMenu] = useState<OriginationItemType>(OriginationItemType.Logs);
    const [logsState, setLogsState] =
        useState<DataStateInterface<OriginationLogModel[]>>(getDefaultState<OriginationLogModel[]>());
    const [logsFilterParams, setLogsFilterParams] = useState<LogsFilterParamsModel>();

    const loadLogs = async (params?: LogsFilterParamsModel) => {
        const curParams = params || logsFilterParams;
        const urlParams = new URLSearchParams({
            length: String(curParams?.length),
        });
        const url = `/api/secured/origination/logs?${urlParams.toString()}`;

        setLogsFilterParams(curParams);
        setLogsState(getLoadingState(logsState));
        const response = await apiRequest(url);
        setLogsState(getLoadedState(response));
    };

    const changeActiveMenu = (value: OriginationItemType) => {
        updateRoute({hash: value, type: null, value: null});
    };

    const handleRouteChange = () => {
        const hash = hashMatch.hash;

        if (hash) {
            const hashMenuItem = hash.toUpperCase() as OriginationItemType;
            const isValidMenuType = Object.values(OriginationItemType).includes(hashMenuItem);

            if (isValidMenuType) {
                setActiveMenu(hashMenuItem);
            }
        }
    };

    useEffect(() => {
        handleRouteChange();
    }, [hashMatch]);

    const value: OriginationContextModel = {
        activeMenu,
        changeActiveMenu,
        logsState,
        loadLogs,
    };

    return <OriginationContext.Provider value={value}>{children}</OriginationContext.Provider>;
};

export function useOrigination() {
    const context = useContext(OriginationContext);
    if (!context) {
        throw new Error('useOrigination must be used within OriginationProvider');
    }
    return context;
}
